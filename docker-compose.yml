services:
  chatgpt-next-web:
    container_name: qadchat
    image: syferie/qadchat:latest
    ports:
      - 3000:3000
    # environment:
    #   # 访问码配置（必需，当设置任何服务商配置时）
    #   - ACCESS_CODE=your-access-code
    #   - OPENAI_API_KEY=sk-your-openai-api-key
    #   - OPENAI_BASE_URL=https://api.openai.com/v1
    #   - GOOGLE_API_KEY=your-google-api-key
    #   - GOOGLE_BASE_URL=https://generativelanguage.googleapis.com
    #   - ANTHROPIC_API_KEY=your-anthropic-api-key
    #   - ANTHROPIC_BASE_URL=https://api.anthropic.com
    #   - AZURE_API_KEY=your-azure-api-key
    #   - AZURE_BASE_URL=https://your-resource.openai.azure.com
    #   - AZURE_API_VERSION=2024-02-01
    #   - BYTEDANCE_API_KEY=your-bytedance-api-key
    #   - BYTEDANCE_BASE_URL=https://ark.cn-beijing.volces.com
    #   - ALIBABA_API_KEY=your-alibaba-api-key
    #   - ALIBABA_BASE_URL=https://dashscope.aliyuncs.com
    #   - MOONSHOT_API_KEY=your-moonshot-api-key
    #   - MOONSHOT_BASE_URL=https://api.moonshot.cn
    #   - DEEPSEEK_API_KEY=your-deepseek-api-key
    #   - DEEPSEEK_BASE_URL=https://api.deepseek.com
    #   - XAI_API_KEY=your-xai-api-key
    #   - XAI_BASE_URL=https://api.x.ai
    #   - SILICONFLOW_API_KEY=your-siliconflow-api-key
    #   - SILICONFLOW_BASE_URL=https://api.siliconflow.cn