# QADChat 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# ================================
# 使用说明
# ================================
# 1. 设置任何服务商的 API_KEY 都会自动启用访问码控制
# 2. BASE_URL 是可选的，如果不设置会使用默认值
# 3. 用户在前端配置的 API 密钥会覆盖这里的服务器配置
# 4. 只有通过访问码验证的用户才能使用服务器配置
# 5. 配置了 baseurl 及 apikey 后，仍需要在模型配置界面启用对应服务商及模型

# ================================
# 访问码配置（必需）
# ================================
# 当设置了任何服务商配置时，必须设置访问码
ACCESS_CODE=your-access-code

# ================================
# OpenAI 配置
# ================================
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# ================================
# Google (Gemini) 配置
# ================================
GOOGLE_API_KEY=your-google-api-key
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com

# ================================
# Anthropic (Claude) 配置
# ================================
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_BASE_URL=https://api.anthropic.com

# ================================
# Azure OpenAI 配置
# ================================
AZURE_API_KEY=your-azure-api-key
AZURE_BASE_URL=https://your-resource.openai.azure.com
AZURE_API_VERSION=2024-02-01

# ================================
# ByteDance (豆包) 配置
# ================================
BYTEDANCE_API_KEY=your-bytedance-api-key
BYTEDANCE_BASE_URL=https://ark.cn-beijing.volces.com

# ================================
# Alibaba (通义千问) 配置
# ================================
ALIBABA_API_KEY=your-alibaba-api-key
ALIBABA_BASE_URL=https://dashscope.aliyuncs.com

# ================================
# Moonshot (月之暗面) 配置
# ================================
MOONSHOT_API_KEY=your-moonshot-api-key
MOONSHOT_BASE_URL=https://api.moonshot.cn

# ================================
# DeepSeek 配置
# ================================
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com

# ================================
# XAI (Grok) 配置
# ================================
XAI_API_KEY=your-xai-api-key
XAI_BASE_URL=https://api.x.ai

# ================================
# SiliconFlow 配置
# ================================
SILICONFLOW_API_KEY=your-siliconflow-api-key
SILICONFLOW_BASE_URL=https://api.siliconflow.cn

